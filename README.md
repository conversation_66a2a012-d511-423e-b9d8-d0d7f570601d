# Cryptocurrency Dashboard

An advanced cryptocurrency trend visualizer built with Streamlit that provides real-time price monitoring, technical indicators, and market screening tools.

## Features

- 📊 Real-time price monitoring from multiple exchanges (Binance, OKX)
- 📈 Technical indicators (RSI, MACD, Bollinger Bands, Ichimoku Cloud)
- 🔔 Smart price alerts
- 📥 Data export capabilities
- 🔍 Market screening tools
- 🎯 Fibonacci retracement levels
- ⚡ Auto-refresh functionality

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Run the application:
```bash
streamlit run crypto_app.py
```

## Usage

1. Select an exchange from the sidebar
2. Choose cryptocurrencies to monitor
3. Set your preferred timeframe and date range
4. Click "Start Monitoring" to begin real-time tracking

## Dependencies

- streamlit>=1.28.0
- ccxt>=4.0.0
- pandas>=1.5.0
- plotly>=5.15.0
- numpy>=1.24.0
- streamlit-autorefresh>=0.0.1 (optional, for auto-refresh)

## Fixed Issues

The following issues have been resolved:
- ✅ Duplicate comment removed
- ✅ Function definition order corrected
- ✅ Variable naming consistency fixed
- ✅ Undefined variable references resolved
- ✅ Improved error handling for edge cases
- ✅ Better default symbol selection logic
- ✅ Fixed "does not have market symbol" errors
- ✅ Improved symbol filtering for better reliability
- ✅ Enhanced Market Screener with better error handling
- ✅ Prioritized USDT pairs for more stable data
- ✅ Reduced verbose error messages in screener

## Note

Make sure you have a stable internet connection as the app fetches real-time data from cryptocurrency exchanges.
